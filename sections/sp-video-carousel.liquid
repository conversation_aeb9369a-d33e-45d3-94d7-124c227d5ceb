{% schema %}
{
  "name": "SP Video Carousel",
  "settings": [
    {
      "type": "text",
      "id": "main_title",
      "label": "Main Title",
      "default": "Watch Our Stories"
    },
    {
      "type": "textarea",
      "id": "main_description",
      "label": "Main Description",
      "default": "Explore our journey through engaging short videos."
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "Button Label",
      "default": "Learn More"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button Link"
    },
    {
      "type": "range",
      "id": "slide_per_view",
      "label": "Slides Fully Visible",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 1
    },
    {
      "type": "range",
      "id": "slide_visibility",
      "label": "Next Slide Visibility (%)",
      "min": 0,
      "max": 100,
      "step": 10,
      "default": 30
    },
    {
      "type": "checkbox",
      "id": "rtl_enable",
      "label": "Enable Right to Left",
      "default": true
    },
    {
      "type": "text",
      "id": "video_aspect_ratio",
      "label": "Video Aspect Ratio (e.g. 16/9 or 4/3)",
      "default": "16/9"
    }
  ],
  "blocks": [
    {
      "type": "video",
      "name": "Video Slide",
      "settings": [
        {
          "type": "url",
          "id": "video_url",
          "label": "Video URL"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Description"
        }
      ]
    }
  ],
  "max_blocks": 10,
  "presets": [
    {
      "name": "SP Video Carousel",
      "category": "Media",
      "blocks": [
        { "type": "video" },
        { "type": "video" },
        { "type": "video" }
      ]
    }
  ]
}
{% endschema %}

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@glidejs/glide/dist/css/glide.core.min.css">

<style>
.sp-video-carousel-wrapper {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  padding-top: 40px;
  padding-bottom: 60px;
  padding-right: 120px;
}
.carousel-left {
  width: 60%;
}
.carousel-right {
  width: 40%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  direction: rtl;
  padding-left: 30px;
}
.carousel-right h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #323438;
  margin-bottom: 20px;
}
.carousel-right p {
  margin: 0 0 30px;
  color: #323438;
}
.carousel-right .icon__button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 24px;
  border-radius: 8px;
  border: 2px solid #007A73 !important;
  background: none;
  color: #007A73;
  font-size: 16px;
  line-height: 1;
  font-weight: 700;
  align-items: center;
  flex-direction: row-reverse;
}
.glide {
  width: 100%;
}
.glide__slide {
  overflow: hidden;
  border-radius: 12px;
}
.glide__slide video {
  width: 100%;
  aspect-ratio: {{ section.settings.video_aspect_ratio | default: '16/9' }};
  object-fit: cover;
}
.glide__arrows {
  display: flex;
  justify-content: flex-start;
  margin-top: 1rem;
}
.glide__arrow {
  background: rgba(255,255,255,0.7);
  padding: 20px 10px;
  border-radius: 0;
  border: none;
  cursor: pointer;
}
.glide__arrow.glide__arrow--left {
  scale: -1 1;
}
@media (max-width: 767px) {
  .sp-video-carousel-wrapper {
    flex-direction: column;
    padding-right: 20px;
  }
  .carousel-left,
  .carousel-right {
    width: 100%;
  }
  .carousel-right {
    padding-left: 0;
    margin-top: 20px;
  }
  /* Enhanced touch interaction for mobile */
  .glide__track {
    touch-action: pan-x;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: grab;
  }
  .glide__track:active {
    cursor: grabbing;
  }
  .glide__slides {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    will-change: transform;
  }
  .glide__slide {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }
  .glide__slide video {
    pointer-events: auto;
    touch-action: manipulation;
  }
  /* Ensure smooth transitions */
  .glide__track[data-glide-el="track"] {
    transition: transform 0.4s cubic-bezier(0.165, 0.840, 0.440, 1.000);
  }
}
</style>

<div class="sp-video-carousel-wrapper">
  <div class="carousel-left">
    <div class="glide" id="videoCarousel">
      <div class="glide__track" data-glide-el="track">
        <ul class="glide__slides">
          {% for block in section.blocks %}
            {% if block.settings.video_url != blank %}
              <li class="glide__slide">
                <video src="{{ block.settings.video_url }}" muted playsinline loop></video>
              </li>
            {% endif %}
          {% endfor %}
        </ul>
      </div>
      <div class="glide__arrows" data-glide-el="controls">
        <button class="glide__arrow glide__arrow--left" data-glide-dir="<">
          <svg width="18" height="8" viewBox="0 0 18 8" xmlns="http://www.w3.org/2000/svg">
            <path d="M4.625 7.125L1.5 4M1.5 4L4.625 0.875M1.5 4H16.5" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
        <button class="glide__arrow glide__arrow--right" data-glide-dir=">">
          <svg width="18" height="8" viewBox="0 0 18 8" xmlns="http://www.w3.org/2000/svg">
            <path d="M4.625 7.125L1.5 4M1.5 4L4.625 0.875M1.5 4H16.5" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
  <div class="carousel-right">
    <h2>{{ section.settings.main_title }}</h2>
    <p>{{ section.settings.main_description }}</p>
    {% if section.settings.button_label and section.settings.button_link %}
    <div class="btn__wrapper">
      <a href="{{ section.settings.button_link }}" class="icon__button">
        <span class="btn__icon">
          <svg width="20" height="10" viewBox="0 0 20 10" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4.75 8.75L1 5M1 5L4.75 1.25M1 5H19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
          </svg>
        </span>
        <span class="btn__text">
          {{ section.settings.button_label }}
        </span>
      </a>
    </div>
    {% endif %}
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/@glidejs/glide/dist/glide.min.js"></script>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const rtl = {{ section.settings.rtl_enable | json }};
    const slideCount = {{ section.settings.slide_per_view | default: 1 }};
    const slideOffset = {{ section.settings.slide_visibility | default: 0 }};
    const perView = slideCount + (slideOffset / 100);

    const glide = new Glide('#videoCarousel', {
      type: 'slider',
      startAt: 0,
      perView: perView,
      focusAt: 'center',
      gap: 20,
      direction: rtl ? 'rtl' : 'ltr',
      animationDuration: 400,
      animationTimingFunc: 'cubic-bezier(0.165, 0.840, 0.440, 1.000)',
      // Disable built-in touch for mobile - we'll handle it manually
      touchRatio: window.innerWidth <= 768 ? 0 : 1,
      breakpoints: {
        768: {
          perView: 1,
          touchRatio: 0 // Disable Glide's touch handling on mobile
        }
      }
    });

    // Mount the glide instance
    glide.mount();

    // Custom swipe handling for mobile
    const carousel = document.querySelector('#videoCarousel');
    const track = carousel.querySelector('.glide__track');
    const slides = carousel.querySelectorAll('.glide__slide');

    // Check if we're on mobile (multiple ways to detect)
    const isMobile = window.innerWidth <= 768 ||
                     /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                     ('ontouchstart' in window);

    // console.log('Is mobile:', isMobile, 'Window width:', window.innerWidth, 'Slides count:', slides.length);

    if (isMobile && slides.length > 1) {
      let startX = 0;
      let startY = 0;
      let currentX = 0;
      let currentY = 0;
      let isSwipeActive = false;
      let currentSlide = 0;
      const totalSlides = slides.length;

      // Touch start
      track.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        isSwipeActive = true;
        // console.log('Touch start:', startX, startY);

        // Prevent video controls
        const video = e.target.closest('video');
        if (video) {
          e.preventDefault();
        }
      }, { passive: false });

      // Touch move
      track.addEventListener('touchmove', function(e) {
        if (!isSwipeActive) return;

        currentX = e.touches[0].clientX;
        currentY = e.touches[0].clientY;

        const deltaX = Math.abs(currentX - startX);
        const deltaY = Math.abs(currentY - startY);

        // If horizontal swipe is dominant, prevent default
        if (deltaX > deltaY && deltaX > 10) {
          e.preventDefault();

          // Visual feedback - slight opacity change
          track.style.opacity = '0.8';
        }
      }, { passive: false });

      // Touch end
      track.addEventListener('touchend', function(e) {
        if (!isSwipeActive) return;
        isSwipeActive = false;

        // Reset visual feedback
        track.style.opacity = '1';

        const deltaX = currentX - startX;
        const deltaY = Math.abs(currentY - startY);
        const threshold = 50;

        // console.log('Touch end - deltaX:', deltaX, 'deltaY:', deltaY, 'threshold:', threshold);

        // Only process horizontal swipes
        if (Math.abs(deltaX) > threshold && Math.abs(deltaX) > deltaY) {
          // console.log('Swipe detected!', deltaX > 0 ? 'right' : 'left');

          // Try Glide.js first
          try {
            if (deltaX > 0) {
              // Swipe right - go to previous slide
              glide.go('<');
            } else {
              // Swipe left - go to next slide
              glide.go('>');
            }
          } catch (error) {
            // console.log('Glide.js failed, using manual navigation:', error);

            // Fallback: manual slide navigation
            if (deltaX > 0) {
              // Previous slide
              const prevBtn = carousel.querySelector('.glide__arrow--left');
              if (prevBtn) prevBtn.click();
            } else {
              // Next slide
              const nextBtn = carousel.querySelector('.glide__arrow--right');
              if (nextBtn) nextBtn.click();
            }
          }
        } else {
          // console.log('Swipe not detected - not enough movement or wrong direction');
        }
      }, { passive: true });

      // Prevent video controls from interfering
      const videos = carousel.querySelectorAll('video');
      videos.forEach(video => {
        video.addEventListener('touchstart', function(e) {
          e.stopPropagation();
        }, { passive: true });
      });
    }

    // Additional fallback: Simple touch detection on the entire carousel
    if (isMobile) {
      let touchStartX = 0;

      carousel.addEventListener('touchstart', function(e) {
        touchStartX = e.touches[0].clientX;
      }, { passive: true });

      carousel.addEventListener('touchend', function(e) {
        const touchEndX = e.changedTouches[0].clientX;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > 50) { // 50px threshold
          // console.log('Fallback swipe detected:', diff > 0 ? 'left' : 'right');

          if (diff > 0) {
            // Swipe left - next slide
            const nextBtn = carousel.querySelector('.glide__arrow--right');
            if (nextBtn) nextBtn.click();
          } else {
            // Swipe right - previous slide
            const prevBtn = carousel.querySelector('.glide__arrow--left');
            if (prevBtn) prevBtn.click();
          }
        }
      }, { passive: true });
    }
  });
</script>
